import { StartScreen, GameOverWinScreen, GameOverLostScreen } from './Scenes'
import { useCallback, useEffect, useRef, useState } from 'react'
import { gameEvents } from '@repo/shared/lib/campaign/eventEmitter'
import { useGame<PERSON>tom } from '@repo/shared/lib/atoms/atom-extensions'
import { defaultGameConfig, ReactGameConfig } from '../types/config'
import { GameScreenId } from '../types/screen'


import { AnswerTile } from './components/AnswerTile'
import { QuestionAreaContainer } from './components/QuestionAreaContainer'

import { QuizTimer } from './components/QuizTimer'
import { makeElementsInteractive } from '@repo/shared-game-utils/utils/previewInteractive'

// Import types from config instead of redefining them
import { QuizAnswer, QuizQuestion } from '../types/config'
import { GameContext, GameRuntimeProps, useGame } from '@repo/shared-game-utils/hooks/useGame'
import { getBackgroundStyle } from '@repo/shared-game-utils/utils/gameStyleUtils'
import { CounterElement } from '@repo/shared-game-utils/components/CounterElement'
import { GameSoundSwitch } from '@repo/shared-game-utils/components/GameSoundSwitch'
import { GameButton } from '@repo/shared-game-utils/components/GameButton'
import { StyledText } from '@repo/shared-game-utils/components/StyledText'
import { usePreloadImage } from '@repo/shared-game-utils/hooks/usePreloadImage'
import { useMusic, useSoundEffect } from '@repo/shared-game-utils/hooks/useSounds'
import { useSetDynamicValue } from '@repo/shared/lib/dynamic-values/hooks/useDynamicValues'
import { RewardMechanics, RewardRollProvider, RewardsProvider, useRewardRollDisplay, useRewards, useRoundReward } from '@repo/shared/lib/rewards/index'
import { createUuid } from '@repo/shared/lib/utils'

// Example: Trigger rewards on round start AND when reaching game-over screens
const REWARD_MECHANICS: RewardMechanics = {
  triggers: [
    { when: 'on_screen_change' as const, screens: ['game-over-win', 'game-over-lost'] }
  ]
}

// Mock quiz data with 2 questions
export const QUIZ_DATA: QuizQuestion[] = [
    {
        text: "Z czym podawany jest groch, według tradycyjnej polskiej potrawy?",
        answers: [
            { text: "Ziemniakami", isCorrect: false },
            { text: "Rodzynkami", isCorrect: false },
            { text: "Kapustą", isCorrect: true },
            { text: "Grzybami", isCorrect: false }
        ]
    },
    {
        text: "Która planeta jest najbliższa Słońcu?",
        answers: [
            { text: "Wenus", isCorrect: false },
            { text: "Merkury", isCorrect: true },
            { text: "Mars", isCorrect: false },
            { text: "Ziemia", isCorrect: false }
        ]
    },
    {
        text: "Jaki jest największy ocean na Ziemi?",
        answers: [
            { text: "Atlantycki", isCorrect: false },
            { text: "Spokojny", isCorrect: true },
            { text: "Indyjski", isCorrect: false },
            { text: "Arktyczny", isCorrect: false }
        ]
    },
    {
        text: "Który pierwiastek chemiczny ma symbol 'O'?",
        answers: [
            { text: "Złoto", isCorrect: false },
            { text: "Srebro", isCorrect: false },
            { text: "Tlen", isCorrect: true },
            { text: "Żelazo", isCorrect: false }
        ]
    },
    {
        text: "W którym roku wybuchła II wojna światowa?",
        answers: [
            { text: "1939", isCorrect: true },
            { text: "1941", isCorrect: false },
            { text: "1945", isCorrect: false },
            { text: "1937", isCorrect: false }
        ]
    },
]



export default function MainGame(props: GameRuntimeProps) {

    
    const getInitialScreen = (): GameScreenId => {
        if (props.currentScreenId) return props.currentScreenId as GameScreenId
        if ((props.config as ReactGameConfig)?.gameStartHandler?.enableStartScreen && !props.isPreview) return 'start'
        return 'main'
    }

    const [currentScreenId, setCurrentScreenId] = useState<GameScreenId>(getInitialScreen())
    const initialGameScreenChecked = useRef(false)

    // Create an enhanced context with both props and screen state
    const contextValue: GameRuntimeProps = {
        ...props,
        defaultConfig: defaultGameConfig,
        currentScreenId,
        setCurrentScreenId,
        initialGameScreenChecked,
    }

    useEffect(() => {
        if (!props.isPreview) return
        setCurrentScreenId(props.currentScreenId || 'main')
    }, [props.currentScreenId, props.isPreview])

    if(!props.config) {
        console.log("Config not yet there. Waiting...")
        return <>Loading</>
    }

    return (
        <GameContext.Provider value={contextValue}>
            <GameWithRewards currentScreenId={currentScreenId} widgetId={props.widgetId} />
        </GameContext.Provider>
    )
}

function GameWithRewards({ currentScreenId, widgetId }: { currentScreenId: string, widgetId: string }) {
    const { roundId } = useGameState()
  

    return (
        <RewardsProvider
            gameWidgetId={widgetId}
            rewardMechanics={REWARD_MECHANICS}
            currentScreen={currentScreenId}
            roundId={roundId}
        >
            <GameContent />
        </RewardsProvider>
    )
}

export const useGameState = () => {
    const { config, widgetId } = useGame<ReactGameConfig>()
    const initialLivesCount = config.gameEndHandler?.useLives ? config.gameEndHandler?.livesCount || 3 : Number.MAX_SAFE_INTEGER

    const [lives, setLivesCount] = useGameAtom(widgetId, 'livesCount', initialLivesCount)
    const [score, setScore] = useState(0)
    const [bestScore, setBestScore] = useGameAtom(widgetId, 'bestScore', 0)
    const [attemptsTaken, setAttemptsTaken] = useGameAtom(widgetId, 'attemptsTaken', 0)
    const [roundId, setRoundId] = useGameAtom(widgetId, 'roundId', "initial")


    useSetDynamicValue(`game/${widgetId}/score`, score)
    


    return {
        lives,
        score,
        bestScore,
        attemptsTaken,
        roundId,
        setLivesCount,
        setScore,
        setBestScore,
        setAttemptsTaken,
        setRoundId,
        generateNewRound: () => setRoundId(createUuid()),
    }
}

function usePrepareAssets() {
    console.log("Prepare assets")
    const { config } = useGame<ReactGameConfig>()

    const [loadedElementsCount, setLoadedElementsCount] = useState(0)
    const elementsToLoad = 5
    const loadPercentage = Math.round((loadedElementsCount / elementsToLoad) * 100)

    function appendLoadedElementCount() {
        setLoadedElementsCount((prev) => prev + 1)
    }

    // Preload sounds
    useMusic(config.backgroundMusic, false)
    useMusic(config.gameOverSound, false)
    useMusic(config.winSound, false)
    useMusic(config.correctAnswerSound, false)
    useMusic(config.incorrectAnswerSound, false)
    useMusic(config.continueToNextQuestionSound, false)

    // Preload images
    usePreloadImage(config.gameOverOverlay?.asset, appendLoadedElementCount)
    usePreloadImage(config.rewardOverlay?.asset, appendLoadedElementCount)
    usePreloadImage(config.outOfLivesOverlay?.asset, appendLoadedElementCount)
    usePreloadImage(config.gameSoundSwitch?.onAsset, appendLoadedElementCount)
    usePreloadImage(config.gameSoundSwitch?.offAsset, appendLoadedElementCount)

    return { loadPercentage }
}

function GameContent() {
    const { config, widgetId, resolveAssetUrl, isPreview, currentScreenId, setCurrentScreenId, onGameAssetSelect } = useGame<ReactGameConfig>()
    const { bestScore, roundId } = useGameState()

      const { rewardRollResult} = useRoundReward(roundId)
    useSetDynamicValue(`game/${widgetId}/rewardRollResult`, rewardRollResult)


    useEffect(() => {

        console.log("Reward roll result changed", rewardRollResult);
        
    }, [rewardRollResult])

    // Preload assets
    const { loadPercentage } = usePrepareAssets()


    // Create a ref for the container
    const containerRef = useRef<HTMLDivElement>(null)

    // Function to handle start button click
    const handleStartButtonClick = useCallback(() => {
        if (isPreview) return

        setCurrentScreenId('main')
    }, [isPreview, setCurrentScreenId])

    // Function to handle game over win button click
    const handleGameOverWinButtonClick = useCallback(() => {
        if (isPreview) return

        gameEvents.emit('GameFinished', {
            score: bestScore,
            widgetId: widgetId,
        })
    }, [isPreview, bestScore, widgetId])

    // Function to handle game over lost button click
    const handleGameOverLostButtonClick = useCallback(() => {
        if (isPreview) return

        gameEvents.emit('GameFinished', {
            score: bestScore,
            widgetId: widgetId,
        })
    }, [isPreview, bestScore, widgetId])



    useEffect(() => {
        if (!isPreview) return
        if(loadPercentage < 100) return

        makeElementsInteractive(onGameAssetSelect)
    }, [currentScreenId, isPreview, loadPercentage])

    if(loadPercentage < 100) {
        return (
            <div className="flex flex-col items-center justify-center h-full w-full">
                <div className="text-xl mb-4">Loading: {loadPercentage}%</div>
                <div className="w-64 h-2 bg-gray-200 rounded-full overflow-hidden">
                    <div
                        className="h-full bg-blue-500 transition-all duration-200 ease-linear"
                        style={{ width: `${loadPercentage}%` }}
                    ></div>
                </div>
            </div>
        )
    }

    return (
        <div
            data-game-widget-id={widgetId}
            ref={containerRef}
            className="flex flex-col items-center justify-center h-full w-full touch-none select-none overscroll-none p-4"
            style={getBackgroundStyle(config.mainBackground, resolveAssetUrl)}
        >
            roundId: {roundId}
            <div className="relative w-full h-full text-black">
                {currentScreenId === 'start' && <StartScreen onButtonClick={handleStartButtonClick} />}
                {currentScreenId === 'main' && <MainGameScreen />}
                {currentScreenId === 'game-over-win' && <GameOverWinScreen onButtonClick={handleGameOverWinButtonClick} />}
                {currentScreenId === 'game-over-lost' && <GameOverLostScreen onButtonClick={handleGameOverLostButtonClick} />}
            </div>
        </div>
    )
}

function MainGameScreen() {
    const { initialGameScreenChecked, config, setCurrentScreenId, isPreview, widgetId, resolveAssetUrl } = useGame<ReactGameConfig>()
    const { lives, score, bestScore, setLivesCount, setScore, setBestScore, setAttemptsTaken, roundId } = useGameState()
    const { handleGameEvent } = useRewards()
    useMusic(config.backgroundMusic, true, true)

    // Get questions from config, fallback to QUIZ_DATA if not available
    const questions = config.questionAnswerSettings?.questions || QUIZ_DATA

    // Sound effects for correct and incorrect answers
    const correctAnswerSoundEffect = useSoundEffect(config.correctAnswerSound)
    const incorrectAnswerSoundEffect = useSoundEffect(config.incorrectAnswerSound)
    const continueToNextQuestionSoundEffect = useSoundEffect(config.continueToNextQuestionSound)

    // State to track the current question index
    const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0)

    useSetDynamicValue(`game/${widgetId}/currentQuestion`, questions[currentQuestionIndex]?.text)

    // State to track which answer buttons should show as correct or incorrect
    type AnswerState = 'default' | 'correct' | 'incorrect'
    const [answerStates, setAnswerStates] = useState<{
        answer1: AnswerState,
        answer2: AnswerState,
        answer3: AnswerState,
        answer4: AnswerState
    }>({
        answer1: 'default',
        answer2: 'default',
        answer3: 'default',
        answer4: 'default'
    })

    // State to track if the current question has been answered
    const [isQuestionAnswered, setIsQuestionAnswered] = useState(false)

    const useLives = config.gameEndHandler?.useLives

    // Start round and fetch reward when roundId changes
    useEffect(() => {
        if (isPreview || !roundId) return

        const startRound = async () => {
            await handleGameEvent('round_start', { roundId })
        }

        startRound()
    }, [roundId, isPreview, handleGameEvent])

    const checkInitialGameScreen = useCallback(
        (lives: number) => {
            if (isPreview) return

            // Handle lives system
            if (useLives && lives <= 0) {
                setCurrentScreenId('game-over-lost')
                return
            }
        },
        [isPreview, useLives, lives, setCurrentScreenId]
    )

    const handleLoseLife = useCallback(async () => {
        if (isPreview) return

        // Increment attempts taken
        setAttemptsTaken((prev: number) => prev + 1)

        // Decrement lives if lives system is enabled
        if (useLives) {
            setLivesCount(lives - 1)
        }

        // If lives system is disabled, continue to next question (no game over)
        if (!useLives) {
            return
        }

        // Handle lives system - check if out of lives
        if (useLives && lives <= 1) {
            // Emit GameFinished event
            gameEvents.emit('GameFinished', {
                score: bestScore,
                widgetId: widgetId,
            });
            // Use setTimeout to delay the screen change so user can see the answer feedback
            setTimeout(() => {
                setCurrentScreenId('game-over-lost')
            }, 1500)
            return
        }

        // If lives system is enabled and player still has lives, just continue playing
        // No need to show try again screen - the feedback is already shown for wrong answers

    }, [isPreview, lives, setLivesCount, setAttemptsTaken, useLives, setCurrentScreenId, bestScore, widgetId])

    // Function to add points to the score
    const handleAddScore = useCallback(() => {
        if (isPreview) return
        const points = 1
        const newScore = score + points
        setScore(newScore)

        if (newScore > bestScore) {
            setBestScore(newScore)
        }
    }, [score, bestScore, setScore, setBestScore, config.gameRewardsHandler])

    // Function to handle timer expiration
    const handleTimeExpired = useCallback(() => {
        if (isPreview || isQuestionAnswered) return;

        const correctAnswerIndex = questions[currentQuestionIndex].answers.findIndex(a => a.isCorrect);
        const correctAnswerKey = `answer${correctAnswerIndex + 1}` as keyof typeof answerStates;

        // Mark question as answered and show correct answer
        setIsQuestionAnswered(true);
        setAnswerStates(prev => ({
            ...prev,
            [correctAnswerKey]: 'correct'
        }));

        // Play incorrect answer sound since time expired
        incorrectAnswerSoundEffect?.play();

        handleLoseLife();
    }, [isPreview, isQuestionAnswered, questions, currentQuestionIndex, answerStates, setIsQuestionAnswered, incorrectAnswerSoundEffect, handleLoseLife]);

    // Function to handle answer selection
    const handleAnswerSelect = useCallback((answer: QuizAnswer, answerIndex: number) => {
        if (isPreview || isQuestionAnswered) return;

        const answerKey = `answer${answerIndex + 1}` as keyof typeof answerStates;
        const correctAnswerIndex = questions[currentQuestionIndex].answers.findIndex(a => a.isCorrect);
        const correctAnswerKey = `answer${correctAnswerIndex + 1}` as keyof typeof answerStates;

        // Pause the timer when an answer is selected
        setIsQuestionAnswered(true);

        if (answer.isCorrect) {
            // This is correct
            setAnswerStates(prev => ({...prev, [answerKey]: 'correct'}));
            handleAddScore();
            // Play correct answer sound
            correctAnswerSoundEffect?.play();
        } else {
            // This is incorrect - show correct answer as well
            setAnswerStates(prev => ({
                ...prev,
                [answerKey]: 'incorrect',
                [correctAnswerKey]: 'correct'
            }));
            // Play incorrect answer sound
            incorrectAnswerSoundEffect?.play();

            handleLoseLife();
        }
    }, [isPreview, isQuestionAnswered, answerStates, questions, currentQuestionIndex, setIsQuestionAnswered, handleAddScore, correctAnswerSoundEffect, incorrectAnswerSoundEffect, handleLoseLife])


    useEffect(() => {
        if (isPreview) return
        if (initialGameScreenChecked.current) return
        console.log('checking initial game screen')
        initialGameScreenChecked.current = true
        checkInitialGameScreen(lives)
    }, [lives])

    return (
        <div className="game-content-area w-full h-full flex flex-col items-center  sm:p-4 space-y-5  relative"> {/* Added items-center, padding, spacing, scroll, and relative */}
            {/* Top section with sound switch, lives counter and score counter using three flex containers */}
            <div className="w-full flex items-center">
                {/* Left container */}
                <div className="flex-1 flex justify-start items-center p-2">
                    {useLives && (config.livesStyle?.position === 'left' || (!config.livesStyle?.position && true)) && (
                        <CounterElement configKey="livesStyle" style={config.livesStyle}>
                            {isPreview ? 3 : lives}
                        </CounterElement>
                    )}
                    {(config.scoreStyle?.position === 'left') && (
                        <CounterElement configKey="scoreStyle" style={config.scoreStyle}>
                            {score}
                        </CounterElement>
                    )}
                    {(config.gameSoundSwitch?.position === 'left' || (config.gameSoundSwitch?.alignment === 'left' && !config.gameSoundSwitch?.position)) && (
                        <GameSoundSwitch config={config.gameSoundSwitch} dataConfigKey="gameSoundSwitch" />
                    )}
                </div>

                {/* Center container */}
                <div className="flex-1 flex justify-center items-center p-2">
                    {useLives && config.livesStyle?.position === 'center' && (
                        <CounterElement configKey="livesStyle" style={config.livesStyle}>
                            {isPreview ? 3 : lives}
                        </CounterElement>
                    )}
                    {(config.scoreStyle?.position === 'center' || !config.scoreStyle?.position) && (
                        <CounterElement configKey="scoreStyle" style={config.scoreStyle}>
                            {score}
                        </CounterElement>
                    )}
                    {(config.gameSoundSwitch?.position === 'center' || (config.gameSoundSwitch?.alignment === 'center' && !config.gameSoundSwitch?.position)) && (
                        <GameSoundSwitch config={config.gameSoundSwitch} dataConfigKey="gameSoundSwitch" />
                    )}
                </div>

                {/* Right container */}
                <div className="flex-1 flex justify-end items-center p-2">
                    {useLives && config.livesStyle?.position === 'right' && (
                        <CounterElement configKey="livesStyle" style={config.livesStyle}>
                            {isPreview ? 3 : lives}
                        </CounterElement>
                    )}
                    {(config.scoreStyle?.position === 'right') && (
                        <CounterElement configKey="scoreStyle" style={config.scoreStyle}>
                            {score}
                        </CounterElement>
                    )}
                    {(config.gameSoundSwitch?.position === 'right' || (config.gameSoundSwitch?.alignment === 'right' && !config.gameSoundSwitch?.position)) && (
                        <GameSoundSwitch config={config.gameSoundSwitch} dataConfigKey="gameSoundSwitch" />
                    )}
                </div>
            </div>



            <QuestionAreaContainer
                config={config.questionAreaContainer}
                dataConfigKey="questionAreaContainer"
                resolveAssetUrl={resolveAssetUrl}
                className="w-full flex flex-col items-center space-y-3 sm:space-y-4 relative z-0"
                questionKey={currentQuestionIndex}
                questionImage={questions[currentQuestionIndex].image}
            >
                <StyledText
                    style={config.questionText}
                    dataConfigKey="questionText"
                    className="px-2"
                >
                    {questions[currentQuestionIndex].text}
                </StyledText>
            </QuestionAreaContainer>

            <QuizTimer
                onTimeExpired={handleTimeExpired}
                questionKey={currentQuestionIndex}
                isPaused={isQuestionAnswered}
            />

            {/* Answer Tiles Area */}
            <div className="w-full flex flex-col items-center gap-3 sm:gap-4">
                {isPreview ? (
                    // In preview mode, show all three button states in rows of 2
                    <>
                        <div className="grid grid-cols-2 gap-3 sm:gap-4 ">
                            <AnswerTile
                                config={config.answerButtonDefault}
                                onClick={() => {}}
                                dataConfigKey="answerButtonDefault"
                                state="default"
                                text="Answer Option 1"
                            />
                            <AnswerTile
                                config={config.answerButtonDefault}
                                onClick={() => {}}
                                dataConfigKey="answerButtonDefault"
                                state="default"
                                text="Answer Option 2"
                            />
                            <AnswerTile
                                config={config.answerButtonCorrect}
                                onClick={() => {}}
                                dataConfigKey="answerButtonCorrect"
                                state="correct"
                                text="Correct Answer"
                            />
                            <AnswerTile
                                config={config.answerButtonIncorrect}
                                onClick={() => {}}
                                dataConfigKey="answerButtonIncorrect"
                                state="incorrect"
                                text="Incorrect Answer"
                            />
                        </div>
                    </>
                ) : (
                    // In game mode, show buttons with their current state in rows of 2
                    <>
                        <div className="grid grid-cols-2 gap-3 sm:gap-4">
                            {questions[currentQuestionIndex].answers.map((answer, index) => {
                                const answerKey = `answer${index + 1}` as keyof typeof answerStates;

                                return (
                                    <AnswerTile
                                        key={index}
                                        config={answerStates[answerKey] === 'default' ? config.answerButtonDefault :
                                               answerStates[answerKey] === 'correct' ? config.answerButtonCorrect :
                                               config.answerButtonIncorrect}
                                        onClick={() => handleAnswerSelect(answer, index)}
                                        dataConfigKey="answerButtonDefault"
                                        state={answerStates[answerKey]}
                                        text={answer.text}
                                    />
                                );
                            })}
                        </div>
                    </>
                )}
            </div>

            {/* Continue Button - only show when question is answered */}
            {(isPreview || isQuestionAnswered) && (
                <GameButton
                    config={config.continueButton}
                    onClick={() => {
                        if (isPreview) return;

                        // If user is out of lives, don't proceed to next question
                        // The timeout in the answer click handler will show the out-of-lives screen
                        if (useLives && lives <= 0) return;

                        // Play continue to next question sound
                        continueToNextQuestionSoundEffect?.play();

                        // Check if we're at the last question
                        if (currentQuestionIndex < questions.length - 1) {
                            // Move to the next question
                            setCurrentQuestionIndex(currentQuestionIndex + 1);

                            // Reset answer states to default
                            setAnswerStates({
                                answer1: 'default',
                                answer2: 'default',
                                answer3: 'default',
                                answer4: 'default'
                            });

                            // Reset question answered state
                            setIsQuestionAnswered(false);
                        } else {
                            console.log('We\'ve reached the end of the quiz');
                            // We've reached the end of the quiz
                            // Emit GameFinished event
                            if (!isPreview) {
                                gameEvents.emit('GameFinished', {
                                    score: bestScore,
                                    widgetId: widgetId,
                                });
                            }
                            
                            // Quiz completed successfully - show win screen
                            console.log('Quiz completed - showing game over win');
                            // Emit GameFinished event when quiz completes
                            gameEvents.emit('GameFinished', {
                                score: bestScore,
                                widgetId: widgetId,
                            });
                            setCurrentScreenId('game-over-win');
                        }
                    }}
                    dataConfigKey="continueButton"
                />
            )}

       

        </div>
    )
}
